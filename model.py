import numpy as np
import pandas as pd
from typing import List, Dict, Tuple
import os


class Material:
    """材料特性类，存储钢种的物理性能参数"""

    def __init__(self, name: str, carbon_content: float,
                 initial_temperature: float, density: float = 7850.0):
        self.name = name  # 钢种名称
        self.carbon_content = carbon_content  # 碳含量(%)
        self.initial_temperature = initial_temperature  # 初始温度(°C)
        self.density = density  # 密度(kg/m³)

    def get_deformation_resistance(self, temperature: float,
                                   strain: float, strain_rate: float) -> float:
        """计算材料在特定温度、应变和应变速率下的变形抗力"""
        # 这里使用简化的变形抗力模型，实际应用中应使用更复杂的模型
        base_resistance = 200 + 500 * self.carbon_content
        temperature_factor = 1.0 - 0.002 * (temperature - 1000)
        strain_factor = 1.0 + 0.1 * np.log(1 + strain)
        rate_factor = 1.0 + 0.05 * np.log(1 + strain_rate)

        return base_resistance * temperature_factor * strain_factor * rate_factor


class RollingMill:
    """轧机参数类，存储轧机的几何和力学参数"""

    def __init__(self, roll_diameter: float, roll_width: float,
                 max_force: float, max_torque: float):
        self.roll_diameter = roll_diameter  # 轧辊直径(m)
        self.roll_width = roll_width  # 轧辊宽度(m)
        self.max_force = max_force  # 最大轧制力(kN)
        self.max_torque = max_torque  # 最大轧制力矩(kN·m)


class RollingPass:
    """轧制道次类，计算单个道次的轧制参数"""

    def __init__(self, mill: RollingMill, material: Material,
                 initial_thickness: float, final_thickness: float,
                 rolling_speed: float):
        self.mill = mill
        self.material = material
        self.initial_thickness = initial_thickness  # 入口厚度(mm)
        self.final_thickness = final_thickness  # 出口厚度(mm)
        self.rolling_speed = rolling_speed  # 轧制速度(m/s)

    def calculate_rolling_force(self) -> float:
        """计算轧制力"""
        reduction = self.initial_thickness - self.final_thickness
        average_thickness = (self.initial_thickness + self.final_thickness) / 2
        contact_length = np.sqrt(self.mill.roll_diameter * reduction / 2) * 1000

        average_temperature = self.material.initial_temperature - 10 * self.rolling_speed
        strain = np.log(self.initial_thickness / self.final_thickness)
        strain_rate = 2 * self.rolling_speed / (self.initial_thickness + self.final_thickness) * 1000

        deformation_resistance = self.material.get_deformation_resistance(
            average_temperature, strain, strain_rate)

        stress_state_factor = 1.0 + 0.1 * strain
        friction_coefficient = 0.4 - 0.0002 * average_temperature

        rolling_force = (contact_length * self.mill.roll_width *
                         deformation_resistance * stress_state_factor *
                         friction_coefficient) / 1000

        return rolling_force

    def calculate_temperature_change(self) -> float:
        """计算轧制过程中的温度变化"""
        reduction = self.initial_thickness - self.final_thickness
        strain = np.log(self.initial_thickness / self.final_thickness)

        heat_generated = 0.9 * strain * self.material.get_deformation_resistance(
            self.material.initial_temperature, strain, 1.0) / self.material.density

        rolling_time = (self.initial_thickness / 1000) / self.rolling_speed
        surface_area = 2 * (self.mill.roll_width * 1000) * np.sqrt(
            self.mill.roll_diameter * reduction / 2)
        heat_loss = 0.05 * surface_area * rolling_time / 1000

        specific_heat = 0.46
        mass = self.mill.roll_width * 1000 * (
                    self.initial_thickness + self.final_thickness) / 2 * self.material.density / 1000 ** 3
        temperature_change = (heat_generated * mass - heat_loss) / (mass * specific_heat)

        return temperature_change


class RollingSchedule:
    """轧制规程类，管理整个粗轧过程的道次计划"""

    def __init__(self, mill: RollingMill, material: Material,
                 initial_thickness: float, target_thickness: float,
                 num_passes: int = 7):
        self.mill = mill
        self.material = material
        self.initial_thickness = initial_thickness  # 初始厚度(mm)
        self.target_thickness = target_thickness  # 目标厚度(mm)
        self.num_passes = num_passes  # 道次数
        self.passes: List[RollingPass] = []
        self.current_material_temperature = material.initial_temperature

    def create_equal_reduction_schedule(self) -> None:
        """创建等压下率的轧制规程"""
        total_reduction_ratio = 1 - self.target_thickness / self.initial_thickness
        average_pass_reduction_ratio = 1 - (1 - total_reduction_ratio) ** (1 / self.num_passes)

        current_thickness = self.initial_thickness

        for i in range(self.num_passes):
            next_thickness = current_thickness * (1 - average_pass_reduction_ratio)

            if i == self.num_passes - 1:
                next_thickness = self.target_thickness

            rolling_speed = 1.0 + i * 0.5

            rolling_pass = RollingPass(
                self.mill,
                Material(
                    self.material.name,
                    self.material.carbon_content,
                    self.current_material_temperature,
                    self.material.density
                ),
                current_thickness,
                next_thickness,
                rolling_speed
            )

            temperature_change = rolling_pass.calculate_temperature_change()
            self.current_material_temperature += temperature_change

            self.passes.append(rolling_pass)
            current_thickness = next_thickness

    def optimize_schedule(self) -> None:
        """优化轧制规程，使各道次轧制力均匀分布"""
        self.create_equal_reduction_schedule()

        max_iterations = 5
        for _ in range(max_iterations):
            forces = [pass_.calculate_rolling_force() for pass_ in self.passes]

            max_force_idx = forces.index(max(forces))
            min_force_idx = forces.index(min(forces))

            if max(forces) / min(forces) > 1.2:
                transfer_ratio = 0.05

                thicknesses = [self.initial_thickness]
                for pass_ in self.passes:
                    thicknesses.append(pass_.final_thickness)

                reduction_to_transfer = (thicknesses[max_force_idx] -
                                         thicknesses[max_force_idx + 1]) * transfer_ratio

                thicknesses[max_force_idx + 1] += reduction_to_transfer
                thicknesses[min_force_idx + 1] -= reduction_to_transfer

                self.passes = []
                self.current_material_temperature = self.material.initial_temperature

                for i in range(self.num_passes):
                    rolling_speed = 1.0 + i * 0.5

                    rolling_pass = RollingPass(
                        self.mill,
                        Material(
                            self.material.name,
                            self.material.carbon_content,
                            self.current_material_temperature,
                            self.material.density
                        ),
                        thicknesses[i],
                        thicknesses[i + 1],
                        rolling_speed
                    )

                    temperature_change = rolling_pass.calculate_temperature_change()
                    self.current_material_temperature += temperature_change

                    self.passes.append(rolling_pass)
            else:
                break

    def get_schedule_summary(self) -> pd.DataFrame:
        """生成轧制规程摘要数据框"""
        data = []
        current_thickness = self.initial_thickness
        current_temperature = self.material.initial_temperature

        for i, pass_ in enumerate(self.passes):
            reduction = current_thickness - pass_.final_thickness
            reduction_ratio = reduction / current_thickness * 100
            force = pass_.calculate_rolling_force()
            temperature_change = pass_.calculate_temperature_change()
            current_temperature += temperature_change

            data.append({
                '道次': i + 1,
                '入口厚度(mm)': current_thickness,
                '出口厚度(mm)': pass_.final_thickness,
                '压下量(mm)': reduction,
                '压下率(%)': reduction_ratio,
                '轧制速度(m/s)': pass_.rolling_speed,
                '轧制力(kN)': force,
                '入口温度(°C)': current_temperature - temperature_change,
                '出口温度(°C)': current_temperature
            })

            current_thickness = pass_.final_thickness

        return pd.DataFrame(data)


class DataLoader:
    """数据加载类，从Excel文件读取输入数据"""

    def __init__(self, file_path: str):
        self.file_path = file_path
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Excel文件不存在: {file_path}")

    def load_material_data(self) -> Material:
        """从Excel读取材料数据"""
        df = pd.read_excel(self.file_path, sheet_name='材料参数')

        if len(df) == 0:
            raise ValueError("材料参数表为空")

        # 假设第一行为材料数据
        row = df.iloc[0]

        return Material(
            name=row['钢种名称'],
            carbon_content=row['碳含量(%)'],
            initial_temperature=row['初始温度(°C)'],
            density=row.get('密度(kg/m³)', 7850.0)
        )

    def load_mill_data(self) -> RollingMill:
        """从Excel读取轧机数据"""
        df = pd.read_excel(self.file_path, sheet_name='轧机参数')

        if len(df) == 0:
            raise ValueError("轧机参数表为空")

        row = df.iloc[0]

        return RollingMill(
            roll_diameter=row['轧辊直径(m)'],
            roll_width=row['轧辊宽度(m)'],
            max_force=row['最大轧制力(kN)'],
            max_torque=row['最大轧制力矩(kN·m)']
        )

    def load_schedule_data(self) -> Dict:
        """从Excel读取轧制规程基本参数"""
        df = pd.read_excel(self.file_path, sheet_name='规程参数')

        if len(df) == 0:
            raise ValueError("规程参数表为空")

        row = df.iloc[0]

        return {
            'initial_thickness': row['初始厚度(mm)'],
            'target_thickness': row['目标厚度(mm)'],
            'num_passes': int(row.get('道次数', 7))
        }


# 使用示例
def main():
    try:
        # 从Excel文件加载数据
        data_loader = DataLoader("D:\work\pycharm\input.xlsx")

        # 创建材料对象
        material = data_loader.load_material_data()

        # 创建轧机对象
        mill = data_loader.load_mill_data()

        # 获取轧制规程参数
        schedule_params = data_loader.load_schedule_data()

        # 创建轧制规程对象
        schedule = RollingSchedule(
            mill=mill,
            material=material,
            initial_thickness=schedule_params['initial_thickness'],
            target_thickness=schedule_params['target_thickness'],
            num_passes=schedule_params['num_passes']
        )

        # 优化轧制规程
        schedule.optimize_schedule()

        # 获取规程摘要
        summary = schedule.get_schedule_summary()

        # 打印结果
        print("热连轧粗轧过程设定计算结果：")
        print(summary)

        # 保存结果到CSV文件
        summary.to_csv("rolling_schedule.csv", index=False)
        print("\n结果已保存至 rolling_schedule.csv")

    except Exception as e:
        print(f"程序执行出错: {str(e)}")


if __name__ == "__main__":
    main()